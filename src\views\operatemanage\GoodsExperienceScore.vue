<template>
  <my-container>
    <template #header>
      <el-date-picker style="width: 205px" v-model="timeRange" type="daterange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" 
        range-separator="至" start-placeholder="起始日期" end-placeholder="结束日期" :picker-options="pickerOptions" 
        @change="changeTime" unlink-panels :clearable="false">
      </el-date-picker>
      <el-select v-model="filter.shopCodeList" placeholder="店铺" clearable filterable multiple collapse-tags style="width: 200px">
        <el-option v-for="item in shopList" :key="item.shopCode" :label="item.shopName" :value="item.shopCode"></el-option>
      </el-select>
      <!-- <el-input v-model="filter.proCode" v-model.trim="filter.proCode" placeholder="产品ID" style="width: 180px;" clearable></el-input> -->
      <button style="width: 185px; padding: 0; border: none;">
        <inputYunhan :inputt.sync="filter.proCode" width="185px" v-model.trim="filter.proCode" :valueOpen="true"  
          placeholder="产品ID/若输入多条请摁回车" :clearable="true" :clearabletext="true" @callback="callback" 
          title="宝贝ID" :maxRows="300" ref="proCode" v-dialogDrag>
        </inputYunhan>
      </button>
      <el-select v-model="filter.goodsStatusList" placeholder="商品状态" clearable filterable multiple collapse-tags>
         <el-option :value="1" label="上架"></el-option>
         <el-option :value="3" label="下架"></el-option>
         <el-option :value="0" label="未知"></el-option>
      </el-select>
      <el-input v-model="filter.styleCode" v-model.trim="filter.styleCode" placeholder="系列编码" style="width: 180px;" clearable></el-input>
      <el-select v-model="filter.groupIdList" placeholder="运营组" clearable filterable multiple collapse-tags style="width: 200px">
        <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
      <el-select v-model="filter.operateSpecialUserIdList" placeholder="运营专员" clearable filterable multiple collapse-tags style="width: 200px">
        <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
      <el-select v-model="filter.userRealNameIdList" placeholder="运营助理" clearable filterable multiple collapse-tags style="width: 200px">
        <el-option v-for="item in directorlist" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>

      <el-button type="primary" @click="onSearch()">查询</el-button>
      <el-button type="primary" @click="onExport()">导出</el-button>
    </template>

    <vxetablebase :id="'GoodsExperienceScore202503011014'" ref="table" :that="that" :isIndex='true' @sortchange='sortchange' 
      :tableData='list' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" :tableFixed="true" 
      v-loading="listLoading" style="width: 100%; margin: 0" :height="'100%'" :showsummary="true" :summaryarry="summaryarry">
    </vxetablebase>
    
    <!--分页-->
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>
  </my-container>
</template>
<script>
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import MyContainer from "@/components/my-container";
import inputYunhan from "@/components/Comm/inputYunhan";
import { formatTime } from "@/utils/tools";
import dayjs from 'dayjs';
import { getDirectorGroupList,getDirectorList,getList as getshopList } from '@/api/operatemanage/base/shop';
import { getGoodsExperienceScore, exportGoodsExperienceScore } from "@/api/operatemanage/GoodsExperienceScore";

const tableCols = [
  { sortable: 'custom', istrue: true, width: '90', align: 'center', label: '日期', prop: 'date', formatter: (row) => formatTime(row.date, 'YYYY-MM-DD') },
  { sortable: 'custom', istrue: true, width: '120', align: 'center', label: '店铺', prop: 'shopName' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '产品ID', prop: 'proCode' },
  { sortable: 'custom', istrue: true, width: '110', align: 'center', label: '标题', prop: 'title' },
  {   sortable: 'custom', istrue: true, width: '100', align: 'center', label: '商品状态', prop: 'goodsStatus', 
    formatter: (row) => {
      let newArr = [
        { value: 1, label: '上架' },
        { value: 3, label: '下架' },
        { value: 0, label: '未知' },
        { value: null, label: '' }
      ];
      return newArr.filter(item => item.value === row.goodsStatus)[0].label;
    }
  },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '系列编码', prop: 'styleCode' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '经营大类', prop: 'bzCategory' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '一级类目', prop: 'bzCategory1' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '二级类目', prop: 'bzCategory2' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '运营组', prop: 'groupName' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '运营专员', prop: 'operateSpecialUser' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '运营助理', prop: 'userRealName' },
  { sortable: 'custom', istrue: true, width: '110', align: 'center', label: '店铺质量体验排名', prop: 'shopQualityRank', formatter: (row) => row.shopQualityRank + '%' },
  { sortable: 'custom', istrue: true, width: '110', align: 'center', label: '商品质量体验排名', prop: 'goodsQualityRank', formatter: (row) => row.goodsQualityRank + '%' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '问题订单数', prop: 'questionOrderNum' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '问题订单占比', prop: 'questionOrderRate', formatter: (row) => row.questionOrderRate + '%' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '评价总数', prop: 'evaluateTotal' },
  { sortable: 'custom', istrue: true, width: '100', align: 'center', label: '商品评价分排名', prop: 'productEvaluationScoreRanking', formatter: (row) => row.productEvaluationScoreRanking == null ? row.productEvaluationScoreRanking : row.productEvaluationScoreRanking + '%' },
];

export default {
  name: "GoodsExperienceScore",
  components: { vxetablebase, MyContainer, inputYunhan },
  data() {
    return {
      that: this,
      listLoading: false,
      tableCols: tableCols,
      list: [],
      total: 0,
      summaryarry: {},
      filter: {
        currentPage: 1,
        pageSize: 50,
        orderBy: 'date',
        isAsc: false,
        //过滤条件
        startDate: '',
        endDate: '',
        shopCodeList: [],
        proCode: '',
        goodsStatusList: [],
        styleCode: '',
        groupIdList: [],
        operateSpecialUserIdList: [],
        userRealNameIdList: [],
      },
      timeRange: [],
      shopList: [],
      groupList: [],
      directorlist: [],

      pickerOptions: {
        shortcuts: [{
        text: '前一天',
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
          end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
          picker.$emit('pick', [start, end]);
        }
        }, {
        text: '近一周',
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
          end.setTime(end.getTime());
          picker.$emit('pick', [start, end]);
        }
        }, {
        text: '近一个月',
        onClick(picker) {
          const date1 = new Date(); date1.setMonth(date1.getMonth() - 1); date1.setDate(date1.getDate());
          const date2 = new Date(); date2.setDate(date2.getDate());
          picker.$emit('pick', [date1, date2]);
        }
        }]
      },
    };
  },
  async mounted() {
    this.init();
  },
  methods: {
    async init() {
      // 默认当天日期
      let start = new Date();
      let end = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
      end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
      this.timeRange = [dayjs(start).format('YYYY-MM-DD'), dayjs(end).format('YYYY-MM-DD')];
      this.filter.startDate = dayjs(start).format('YYYY-MM-DD');
      this.filter.endDate = dayjs(end).format('YYYY-MM-DD');
      
      const res1 = await getshopList({ platform: 2, CurrentPage:1,PageSize:100000 });
      this.shopList=res1.data.list;

      var res2= await getDirectorGroupList();
      this.groupList = res2.data?.map(item => {return { value: item.key, label: item.value };}); 

      var res3= await getDirectorList();
      this.directorlist = res3.data?.map(item => {return { value: item.key, label: item.value };}); 
  
      this.onSearch();
    },
    async changeTime(e) {
      this.filter.startDate = e? e[0] : null;
      this.filter.endDate = e? e[1] : null;
    },
    async callback(val) {
      this.filter.proCode = val;
    },
    // 排序
    async sortchange({ order, prop }) {
      if (prop) {
        this.filter.orderBy = prop;
        this.filter.isAsc = order.indexOf("descending") == -1 ? true : false;
        this.onSearch();
      }
    },
    // 每页数量改变
    Sizechange(val) {
      this.filter.currentPage = 1;
      this.filter.pageSize = val;
      this.getList();
    },
    // 当前页改变
    Pagechange(val) {
      this.filter.currentPage = val;
      this.getList();
    },
    async onSearch() {
      //点击查询时才将页数重置为1
      this.filter.currentPage = 1;
      this.$refs.pager.setPage(1);
      this.getList();
    },
    async getList() {
      this.listLoading = true;
      let data, success;
      ({ data, success } = await getGoodsExperienceScore(this.filter));
      this.listLoading = false;
      if (success) {
        this.list = data.list;
        this.total = data.total;
        this.summaryarry = data.summary;
      } else {
        this.$message.error("获取数据失败！");
      }
    },
    // 导出
    async onExport() {
      this.listLoading = true;
      const res = await exportGoodsExperienceScore(this.filter);
      this.listLoading = false;
      if (!res?.data) return;
      const aLink = document.createElement("a");
      let blob = new Blob([res.data], { type: "application/vnd.ms-excel" });
      aLink.href = URL.createObjectURL(blob);
      aLink.setAttribute("download", "商品体验评分-" + new Date().toLocaleString() + '.xlsx');
      aLink.click();
    },
  }
};
</script>
<style lang="scss" scoped>
::v-deep .el-select__tags-text {
  max-width: 70px;
}
</style>
