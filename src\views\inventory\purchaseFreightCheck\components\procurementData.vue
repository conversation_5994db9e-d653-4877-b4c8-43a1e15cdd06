<template>
  <MyContainer>
    <template #header>
      <div class="top">
        <el-date-picker v-model="timeRanges" type="daterange" unlink-panels range-separator="至"
          start-placeholder="审核开始日期" end-placeholder="审核结束日期" :picker-options="pickerOptions"
          style="width: 250px;margin-right: 5px;" :value-format="'yyyy-MM-dd'" @change="changeTime">
        </el-date-picker>
        <el-select v-model="ListInfo.status" placeholder="状态" class="publicCss" clearable multiple collapse-tags>
          <el-option key="正常" label="正常" value="正常" />
          <el-option key="异常" label="异常" value="异常" />
          <el-option key="待审核" label="待审核" value="待审核" />
          <el-option key="待处理" label="待处理" value="待处理" />
        </el-select>
        <div style="display: flex;gap: 5px;margin-right: 5px;">
          <inputYunhan ref="buyNo" :inputt.sync="ListInfo.buyNo" v-model="ListInfo.buyNo" :valuedOpen="true"
            width="140px" placeholder="采购单号/若输入多条请按回车" :clearable="true" :clearabletext="true" :maxRows="100"
            :maxlength="1000" @callback="callbackbuyNo" title="采购单号">
          </inputYunhan>
        </div>
        <el-select v-model="ListInfo.warehouse" placeholder="仓库" class="publicCss" clearable filterable multiple
          collapse-tags>
          <el-option v-for="item in warehouselist" :key="item.name" :label="item.name" :value="item.wms_co_id" />
        </el-select>
        <el-select v-model="ListInfo.payment" placeholder="采购单类型" class="publicCss" clearable multiple collapse-tags>
          <el-option key="包邮" label="包邮" value="包邮" />
          <el-option key="寄付" label="寄付" value="寄付" />
          <el-option key="仓库到付" label="仓库到付" value="仓库到付" />
        </el-select>
        <el-select v-model="ListInfo.brandId" placeholder="采购" class="publicCss" clearable filterable multiple
          collapse-tags>
          <el-option v-for="item in brandlist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select filterable v-model.trim="ListInfo.deptIdList" clearable multiple collapse-tags placeholder="架构"
          class="publicCss">
          <el-option v-for="(item, index) in purchasegrouplist" :key="item.value + index" :label="item.label"
            :value="item.value" />
        </el-select>
        <el-input v-model.trim="ListInfo.supplier" placeholder="供应商" maxlength="64" clearable class="publicCss" />
        <el-select v-model="ListInfo.problemGroup" placeholder="问题归类" class="publicCss" clearable filterable>
          <el-option key="厂家问题" label="厂家问题" value="厂家问题"></el-option>
          <el-option key="采购问题" label="采购问题" value="采购问题"></el-option>
          <el-option key="仓库问题" label="仓库问题" value="仓库问题"></el-option>
          <el-option key="其他" label="其他" value="其他"></el-option>
        </el-select>
        <el-button type="primary" @click="getList('search')">搜索</el-button>
      </div>
      <div class="top">
        <el-button type="primary" @click=exportProps>导出</el-button>
        <el-button type="primary" @click="onSetRules">设置规则</el-button>
        <el-dropdown @command="onBatchVerify" style="margin-left: 10px;">
          <el-button style="height: 29px;" type="primary">
            批量操作<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item :command=1>处理</el-dropdown-item>
            <el-dropdown-item :command=2>确认</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </template>
    <vxetablebase :ispoint="false" :id="'procurementData202410160912'" :tablekey="'procurementData202302031421'"
      ref="table" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchange'
      :tableData='tableData' :tableCols='tableCols' :isSelection="false" :isSelectColumn="false" @select="selectchange"
      :summaryarry='summaryarry' :showsummary='true' style="width: 100%;  margin: 0" :loading="loading" :height="'100%'"
      :treeProp="{ rowField: 'id', parentField: 'parentId', transform: true }">
      <template slot="right">
        <vxe-column title="操作" width="120" fixed="right">
          <template #default="{ row, $index }">
            <div class="tableCss">
              <el-button type="text" @click="onVerify(row, 1)" v-if="isDisabled(row, 'confirm')">确认
              </el-button>
              <el-button type="text" @click="onVerify(row, 2)" v-if="isDisabled(row, 'process')">处理
              </el-button>
              <el-button type="text" @click="onLogPageMethod(row)" v-if="isDisabled(row, 'log')">日志
              </el-button>
            </div>
          </template>
        </vxe-column>
      </template>
      <template #freight="{ row }">
        <div class="tableCss">
          {{ row.freight !== null && row.freight !== undefined ? parseFloat(row.freight) : '-' }}
        </div>
      </template>
      <template #haulage="{ row }">
        <div class="tableCss">
          {{ row.haulage !== null && row.haulage !== undefined ? parseFloat(row.haulage) : '-' }}
        </div>
      </template>
      <template #deliveryFee="{ row }">
        <div class="tableCss">
          {{ row.deliveryFee !== null && row.deliveryFee !== undefined ? parseFloat(row.deliveryFee) : '-' }}
        </div>
      </template>
      <template #pickUpFee="{ row }">
        <div class="tableCss">
          {{ row.pickUpFee !== null && row.pickUpFee !== undefined ? parseFloat(row.pickUpFee) : '-' }}
        </div>
      </template>
      <template #huoLaLa="{ row }">
        <div class="tableCss">
          {{ row.huoLaLa !== null && row.huoLaLa !== undefined ? parseFloat(row.huoLaLa) : '-' }}
        </div>
      </template>
      <template #loadingFee="{ row }">
        <div class="tableCss">
          {{ row.loadingFee !== null && row.loadingFee !== undefined ? parseFloat(row.loadingFee) : '-' }}
        </div>
      </template>
    </vxetablebase>
    <template #footer>
      <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
    </template>

    <el-dialog title="设置规则" :visible.sync="setUpRules" width="50%" v-dialogDrag style="margin-top: -5vh;">
      <setRulesPage ref="refsetRulesPage" v-if="setUpRules" @setRulesPageClose="setRulesPageClose" />
    </el-dialog>

    <el-dialog :title="distinguish ? '确认' : '处理'" :visible.sync="verifyHandleVisible" width="45%" v-dialogDrag
      style="margin-top: -5vh;">
      <verifyHandle ref="refverifyHandle" :editid=editid :distinguish="distinguish" :isParent="isParent"
        v-if="verifyHandleVisible" @verifyClose="verifyHandleVisible = false" @verifyHandleClose="verifyHandleClose" />
    </el-dialog>

    <el-dialog title="日志" :visible.sync="logPageVisible" width="50%" v-dialogDrag style="margin-top: -5vh;">
      <div style="height: 400px;">
        <logPage ref="reflogPage" v-if="logPageVisible" :logsid="logsid" :logIsParent="logIsParent" />
      </div>
    </el-dialog>

    <el-dialog :title="batchVerify.title" :visible.sync="batchVerify.visible" width="45%" v-dialogDrag
      style="margin-top: -5vh;">
      <batchVerifyHandle ref="refBatchVerifyHandle" :editids="batchVerify.editids"
        :distinguish="batchVerify.distinguish" v-if="batchVerify.visible"
        @batchVerifyClose="batchVerify.visible = false" @batchVerifyHandleClose="batchVerifyVisibleClose" />
    </el-dialog>

  </MyContainer>
</template>

<script>
import MyContainer from "@/components/my-container";
import vxetablebase from "@/components/VxeTable/yh_vxetableNotFixNum.vue";
import { pickerOptions } from '@/utils/tools'
import inputYunhan from "@/components/Comm/inputYunhan";
import { getAllProBrand, getAllWarehouse } from '@/api/inventory/warehouse'
import setRulesPage from "./setRulesPage.vue";
import { getPurchaseCostVerifyPurchaseOrderPage, exportPurchaseCostVerifyPurOrder } from '@/api/inventory/purchaseCostVerify'
import { getPurchaseNewPlanTurnDayDeptList } from '@/api/inventory/purchaseordernew'
import verifyHandle from "./verifyHandle.vue";
import batchVerifyHandle from "./batchVerifyHandle.vue";
import logPage from "./logPage.vue";
import dayjs from 'dayjs'
const tableCols = [
  { istrue: true, width: '40', type: "checkbox" },
  { sortable: 'custom', width: '100', align: 'center', prop: 'buyNo', label: '采购单号', treeNode: true, },
  { sortable: 'custom', width: '100', align: 'center', prop: 'brandName', label: '采购', },
  { width: '185', align: 'center', prop: 'deptName', label: '架构', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'payment', label: '采购单类型', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'checkDate', label: '审核日期', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'warehouseName', label: '仓库', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'supplier', label: '供应商', },
  { summarystyle: { color: 'red' }, width: '100', align: 'center', prop: 'status', label: '状态', },
  { sortable: 'custom', width: '100', align: 'center', prop: 'freight', label: '采购运费', },
  { width: '100', align: 'center', prop: 'haulage', label: '托运费' },
  { width: '100', align: 'center', prop: 'deliveryFee', label: '送货费', },
  { width: '100', align: 'center', prop: 'pickUpFee', label: '提货费', },
  { width: '100', align: 'center', prop: 'huoLaLa', label: '货拉拉', },
  { width: '100', align: 'center', prop: 'loadingFee', label: '装车费', },
  { width: '100', align: 'center', prop: 'picture', label: '图片', type: "images", },
  { width: '100', align: 'center', prop: 'remark', label: '备注', },
  { width: '100', align: 'center', prop: 'problemGroup', label: '问题归类' }
]
export default {
  name: "procurementData",
  components: {
    MyContainer, vxetablebase, setRulesPage, verifyHandle, batchVerifyHandle, logPage, inputYunhan
  },
  data() {
    return {
      isParent: false,
      logsid: 0,
      logIsParent: 0,
      logPageVisible: false,
      distinguish: false,
      verifyHandleVisible: false,
      editid: '',
      setUpRules: false,
      brandlist: [],//采购
      warehouselist: [],//仓库
      purchasegrouplist: [],//架构
      that: this,
      ListInfo: {
        currentPage: 1,
        pageSize: 50,
        orderBy: null,
        isAsc: false,
        startDate: null,//开始时间
        endDate: null,//结束时间
        status: ['待处理'],//状态
        buyNo: '',//采购单号
        warehouse: [],//仓库
        payment: [],//采购单类型
        brandId: [],//采购
        supplier: '',//供应商
        deptIdList: [],//架构
      },
      timeRanges: [],
      tableCols,
      tableData: [],
      summaryarry: {},
      total: 0,
      loading: false,
      pickerOptions,
      sels: [],
      batchVerify: {
        title: null,
        visible: false,
        picture: null,
        remark: null,
        editids: [],
        distinguish: false
      },
    }
  },
  async mounted() {
    await this.init()
    await this.getList()
  },
  methods: {
    setRulesPageClose() {
      this.setUpRules = false
      this.getList()
    },
    verifyHandleClose() {
      this.verifyHandleVisible = false
      this.getList()
    },
    batchVerifyVisibleClose() {
      this.batchVerify.visible = false;
      this.getList();
    },
    isDisabled(row, buttonType) {
      if (row.parentId == null) {
        return true
      }
      const status = row.status;
      return status === '待处理' || status === '正常' || status === '异常';
    },
    //日志
    onLogPageMethod(row) {
      this.logsid = row.id
      this.logIsParent = row.parentId > 0 ? 0 : 1;
      this.logPageVisible = true
    },
    //确认处理
    onVerify(row, val) {
      if (val == 1) {
        this.distinguish = true
      } else {
        this.distinguish = false
      }
      if (row.parentId == null) {
        this.isParent = true
      } else {
        this.isParent = false
      }
      this.editid = row.id
      this.verifyHandleVisible = true
    },
    //设置规则
    onSetRules() {
      this.setUpRules = true
    },
    // 采购单号
    callbackbuyNo(val) {
      this.ListInfo.buyNo = val
    },
    //初始化数据
    async init() {
      let res2 = await getAllProBrand();
      this.brandlist = res2.data ? res2.data.map(item => { return { value: item.key, label: item.value }; }) : [];
      let res3 = await getAllWarehouse();
      this.warehouselist = res3.data ? res3.data.filter((x) => x.name.indexOf('代发') < 0) : [];
      //架构
      let { data: deptList, success } = await getPurchaseNewPlanTurnDayDeptList();
      if (success) {
        this.purchasegrouplist = deptList.map(item => { return { value: item.dept_id, label: item.full_name }; });
      }
    },
    async changeTime(e) {
      this.ListInfo.startDate = e ? e[0] : null
      this.ListInfo.endDate = e ? e[1] : null
    },
    async exportProps() {
      this.loading = true
      const params = this.queryCondition()
      await exportPurchaseCostVerifyPurOrder(params);
      this.loading = false
    },
    queryCondition() {
      return {
        ...this.ListInfo,
        currentPage: this.ListInfo.currentPage,
        pageSize: this.ListInfo.pageSize,
        startDate: this.ListInfo.startDate,
        endDate: this.ListInfo.endDate,
        status: this.ListInfo.status?.length ? this.ListInfo.status.join(',') : '',
        buyNo: this.ListInfo.buyNo,
        warehouse: this.ListInfo.warehouse?.length ? this.ListInfo.warehouse.join(',') : '',
        payment: this.ListInfo.payment?.length ? this.ListInfo.payment.join(',') : '',
        brandId: this.ListInfo.brandId?.length ? this.ListInfo.brandId.join(',') : '',
        supplier: this.ListInfo.supplier,
        problemGroup: this.ListInfo.problemGroup,
        orderBy: this.ListInfo.orderBy,
        isAsc: this.ListInfo.isAsc
      }
    },
    async getList(type) {
      if (type == 'search') {
        this.ListInfo.currentPage = 1
        this.$refs.pager.setPage(1)
      }
      if (this.timeRanges && this.timeRanges.length == 0) {
        //默认给当前月第一天至今天
        this.ListInfo.startDate = dayjs().startOf('month').format('YYYY-MM-DD')
        this.ListInfo.endDate = dayjs().format('YYYY-MM-DD')
        this.timeRanges = [this.ListInfo.startDate, this.ListInfo.endDate]
      }
      this.loading = true
      const params = this.queryCondition()
      if (this.checkInputLength(params.buyNo, 'buyNo', '采购单号只能输入100个')) return;
      const { data, success } = await getPurchaseCostVerifyPurchaseOrderPage(params)
      this.loading = false
      if (success) {
        this.tableData = data.list
        this.tableData.forEach(item => {
          item.checkDate = item.checkDate ? dayjs(item.checkDate).format('YYYY-MM-DD') : '';
        });
        this.total = data.total
        let summary = data.summary || {};
        Object.entries(summary).forEach(([key, value]) => {
          if (typeof value !== 'string') {
            summary[key] = String(value);
          }
        });
        this.summaryarry = summary
      } else {
        this.$message.error('获取列表失败')
      }
    },
    checkInputLength(param, paramName, errorMessage) {
      if (param) {
        let values = param.split(',');
        if (values.length > 100) {
          this.$message.error(errorMessage);
          this.loading = false;
          return true;
        }
      }
      return false;
    },
    //每页数量改变
    Sizechange(val) {
      this.ListInfo.currentPage = 1;
      this.ListInfo.pageSize = val;
      this.getList()
    },
    //当前页改变
    Pagechange(val) {
      this.ListInfo.currentPage = val;
      this.getList()
    },
    sortchange({ order, prop }) {
      if (prop) {
        this.ListInfo.orderBy = prop
        this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
        this.getList()
      }
    },
    // 复选框数据
    selectchange(val) {
      this.sels = val
    },
    onBatchVerify(command) {

      if (this.sels.length == 0) {
        this.$message({ message: "请选择需要操作的数据！", type: "warning" });
        return false;
      }

      var ids = [];
      this.sels.map(item => {
        if (item.parentId > 0)
          ids.push(item.id);
      });

      if (ids.length == 0) {
        this.$message({ message: "勾选数据中没有子级数据！", type: "warning" });
        return false;
      }

      this.batchVerify.editids = ids;

      if (command == 1) {
        this.batchVerify.title = "批量处理";
        this.batchVerify.distinguish = false;
      } else {
        this.batchVerify.title = "批量确认";
        this.batchVerify.distinguish = true;
      }

      this.batchVerify.visible = true;
    }
  }
}
</script>

<style scoped lang="scss">
.top {
  display: flex;
  margin-bottom: 10px;

  .publicCss {
    width: 170px;
    margin-right: 5px;
  }
}

.tableCss {
  display: flex;
  justify-content: center;
  align-items: center;
}

::v-deep .el-select__tags-text {
  max-width: 40px;
}
</style>
