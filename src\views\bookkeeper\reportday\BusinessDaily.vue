<template>
    <my-container v-loading="pageLoading">
        <!-- 搜索条件 -->
        <template #header>
            <el-date-picker style="width: 280px" v-model="filter.daterange" type="daterange" format="yyyy-MM-dd"
                value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                :clearable="false" :picker-options="pickerOptions"></el-date-picker>

            <el-input v-model.trim="filter.proCode" clearable placeholder="商品ID" style="width:140px" maxlength="50" />

            <el-select v-model="filter.shopCode" filterable clearable placeholder="店铺" style="width:160px">
                <el-option v-for="item in shopList" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
            </el-select>
            <el-select v-model.trim="filter.business" filterable clearable placeholder="商务" style="width:140px">
                <el-option v-for="item in businessManList" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
            </el-select>

            <el-select v-model.trim="filter.expertId" filterable clearable placeholder="达人ID" remote
                reserve-keyword :remote-method="getExpertIdList" :loading="expertLoading"
                style="width:200px">
                <el-option v-for="item in expertIdList" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
            </el-select>

            <el-input v-model.trim="filter.keywords" clearable placeholder="关键字查询 商品/店铺/达人" style="width:240px" maxlength="50" />

            <el-button-group>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onImport">导入</el-button>
            </el-button-group>
        </template>

        <!-- 表格 -->
        <vxetablebase :id="'BusinessDaily202507261306'" :border="true" :align="'center'"
            :tablekey="'BusinessDaily202507261306'" ref="table" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :isSelectColumn="true" :showsummary='true' :tablefixed='true'
            :summaryarry='summaryarry' :tableData='datalist' :tableCols='tableCols'
            :loading="listLoading" style="width:100%;height:100%;margin: 0" :xgt="9999">
        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" @get-page="getList" />
        </template>

        <!-- 导入 -->
        <el-dialog title="导入" :visible.sync="importDaily.isShow" width="30%" v-dialogDrag :close-on-click-modal="false">
            <div class="upload-section">
                <div class="upload-row">
                    <div style="margin-bottom: 5px;">
                        <el-date-picker v-model="importDaily.yearMonthDay" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="日期"></el-date-picker>
                    </div>
                    <el-upload ref="upload" class="upload-area" :auto-upload="false" :multiple="false" :limit="1" action
                        accept=".xlsx" :file-list="importDaily.fileList" :data="importDaily.fileparm" :http-request="onUploadFile"
                        :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
                        <template #trigger>
                        <el-button size="small" type="primary">选取文件</el-button>
                        </template>
                        <el-button size="small" type="success" :loading="importDaily.loading" @click="onSubmitUpload" class="upload-btn">
                        {{ importDaily.loading ? '上传中' : '上传' }}
                        </el-button>
                    </el-upload>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="importDaily.isShow = false">关闭</el-button>
            </span>
        </el-dialog>


    </my-container>
</template>
<script>
import dayjs from "dayjs";
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import { getAllList as getAllShopList } from '@/api/operatemanage/base/shop';
import { GetAllBusinessDetailList } from "@/api/bookkeeper/reportdayDouYin";
import { GetVideoBusinessDayReportList, ImportVideoBusinessDayReport, GetVideoExpertManagementList } from "@/api/bookkeeper/reportdayV2";
import { formatTime, pickerOptions,formatLinkProCode } from "@/utils/tools";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";

const tableCols = [
    { istrue: true, prop: 'yearMonthDay', label: '年月日', sortable: 'custom', width: '80', formatter: (row) => dayjs(row.yearMonthDay).format('YYYY-MM-DD') },
    { istrue: true, prop: 'shopName', label: '店铺名称', sortable: 'custom', width: '160' },
    { istrue: true, label: '小组头像',  width: '70',type:'ddAvatar',ddInfo:{type:1,prop:'groupId'} },
    { istrue: true, prop: 'groupName', label: '运营组', sortable: 'custom', width: '80',type:'ddTalk',ddInfo:{type:1,prop:'groupId',name:'groupName'}, },
    { istrue: true, prop: 'business', label: '商务', sortable: 'custom', width: '90' },
    { istrue: true, prop: 'expert', label: '达人', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'expertId', label: '达人ID', sortable: 'custom', width: '85' },
    { istrue: true, prop: 'proCode', align: "center", label: '商品ID', sortable: 'custom', width: '120',type:'html',formatter:(row) => formatLinkProCode(20,row.proCode,) },
    { istrue: true, prop: 'proName', label: '商品名称', sortable: 'custom', minwidth: '120' },
    { istrue: true, prop: 'listingTime', label: '上架时间', sortable: 'custom', width: '140' },
    { istrue: true, prop: 'orderNumber', label: '订单量', sortable: 'custom', width: '80' },
    { istrue: true, prop: 'paymentAmount', label: '付款金额', sortable: 'custom', width: '80', formatter: (row) => row.paymentAmount },
    { istrue: true, prop: 'commissionRate', label: '佣金率', sortable: 'custom', width: '80', formatter: (row) => row.commissionRate + "%" },
    { istrue: true, prop: 'commission', label: '佣金', sortable: 'custom', width: '80', formatter: (row) => row.commission },
];


export default {
    name: "BusinessDaily",
    components: { MyContainer, cesTable, vxetablebase },
    data() {
        return {
            that: this,
            businessManList: [],
            filter: {
                daterange: [formatTime(dayjs().subtract(3, 'day'), "YYYY-MM-DD"), formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD")],
                startDate: null,
                endDate: null,
                proCode: null,
                shopCode:null,
                keywords: null,
                expertId: null
            },
            tableCols,
            pickerOptions,
            pager: { OrderBy: "createdTime", IsAsc: false },
            shopList:[],
            expertIdList:[],
            expertLoading: false,
            total:0,
            summaryarry:{},
            datalist:[],
            listLoading: false,
            pageLoading: false,

            importDaily:{
                isShow:false,
                loading:false,
                fileList:[],
                fileparm: null,
                yearMonthDay: formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD")
            }

        };
    },
    async mounted() {

    },
    async created() {
        this.getBusinessNameList()
        this.getShop()
        this.getExpertIdList();
    },
    methods: {
        //获取商务
        async getBusinessNameList() {
            const resBusiness = await GetAllBusinessDetailList();
            this.businessManList = resBusiness.data?.map(item => { return { value: item.businessMan, label: item.businessMan }; });
        },
        //获取店铺
        async getShop(){
            const res = await getAllShopList({ platforms: [20] });
            this.shopList = res.data?.map(item => { return { value: item.shopCode, label: item.shopName }; });
        },
        //获取达人ID
        async getExpertIdList(value){
            const res = await GetVideoExpertManagementList({ expertId: value, currentPage: 1, pageSize: 20, OrderBy:'createdTime', IsAsc:false });
            this.expertIdList = res.data.list?.map(item => {
                return { value: item.expertId, label: item.expert + '（达人ID:' + item.expertId + '）' };
            });
        },
        //搜索
        onSearch(){
            this.$refs.pager.setPage(1);
            this.getList();
        },
        //获取列表数据
        async getList() {
            this.filter.startDate = null;
            this.filter.endDate = null;
            if (this.filter.daterange) {
                this.filter.startDate = this.filter.daterange[0];
                this.filter.endDate = this.filter.daterange[1];
            }
            var that = this;
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            this.pageLoading = true;
            const res = await GetVideoBusinessDayReportList(params);
            this.pageLoading = false;
            that.total = res.data?.total;
            that.datalist = res.data?.list;
            that.summaryarry = res.data?.summary;
        },
        //排序
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },

        //导入弹窗
        onImport(){
            this.importDaily = {
                isShow:true,
                loading:false,
                fileList:[],
                fileparm: null,
                yearMonthDay: formatTime(dayjs().subtract(1, 'day'), "YYYY-MM-DD")
            }
        },
        //移除文件
        onUploadRemove(file, fileList) {
            this.importDaily.fileList = []
        },
        //添加文件
        onUploadChange(file, fileList) {
            this.importDaily.fileList = fileList;
        },
        onUploadSuccess(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.importDaily.fileList = [];
            this.importDaily.isShow = false;
        },
        //上传验证
        onSubmitUpload() {
            if (this.importDaily.fileList.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.upload.submit();
        },
        //提交导入
        async onUploadFile(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.importDaily.loading = true
            const form = new FormData();
            form.append("upfile", item.file);
            form.append("yearMonthDay", this.importDaily.yearMonthDay);
            var res = await ImportVideoBusinessDayReport(form);
            if (res?.success)
                this.$message({ message: res.message || "上传成功,正在导入中...", type: "success" });
            this.importDaily.loading = false
            this.importDaily.isShow = false;
            this.onSearch()
        },
    }
};
</script>

