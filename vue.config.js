const CopyWebpackPlugin = require("copy-webpack-plugin")
const path = require('path')
const defaultSettings = require('./src/settings.js')
function resolve(dir) { return path.join(__dirname, dir)}

const domain = process.env.VUE_APP_DOMAIN
const isDev = process.env.NODE_ENV === 'development'
const name = defaultSettings.title || '昀晗贸易有限公司'
require('events').EventEmitter.defaultMaxListeners = 20; // 将MaxListeners的限制数增加到20
//const TestPath = process.env.VUE_APP_BASE_API_UpLoadNew_Domin_Test;
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
// const CompressionPlugin = require('compression-webpack-plugin');

// if (process.env.npm_config_pages==process.env.VUE_APP_MODE){
//   process.env.VUE_APP_BASE_API_UpLoadNew_Domin = TestPath;
//   process.env.VUE_APP_DOMAIN_NAME = TestPath;
//   process.env.VUE_APP_BASE_API_UpLoad_Video = process.env.VUE_APP_BASE_API_UpLoad_Video_Dev
// }


// 官方配置说明 https://cli.vuejs.org/zh/config/#vue-config-js
module.exports = {
  // 基本路径
  publicPath: '/',
  // 输出文件目录
  // outputDir: 'dist',
  //assetsDir: 'static',
  //assetsSubDirectory: 'static',
  lintOnSave: false,
  productionSourceMap: false,
  // entry: './src/main.js',


  devServer: {
    // 自动启动浏览器
    open: false,
    port: 8002,
    // 浏览器弹出错误
    overlay: {
      warnings: false,
      errors: true
    },
    // 配置多个代理
    // detail: https://cli.vuejs.org/config/#devserver-proxy
    proxy: {
      ['^' + process.env.VUE_APP_BASE_API]: {
        target: 'http://*************:8081',
        // target: 'http://localhost:8000',
        // target: domain,
        // target: 'http://*************:8081',
        // target: 'http://**************:8000',
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_MsgCenter]: {
        //target: 'http://*************:8081',
        // target: 'http://localhost:8000',
        target: domain,
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_Express]: {
        // target: domain+':8010',
        // target: domain,
        target: 'http://*************:8081',
        // target: 'http://**************:8010',
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_Financial]: {
        // target: domain+':8060',
        target: domain,
        // target: 'http://**************:8060',
        // target: 'http://*************:8081',
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_Order]: {
        //target: 'http://localhost'+':8020',
        // target: domain,
        target: 'http://**************:8020',
        // target: 'http://*************:8081',
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_OperateManage]: {
        // target: 'http://localhost:8030',
        // target: 'http://**************:8030',
        target: domain,
        // target: 'http://*************:8081',
        // target: 'http://***************:8030',
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_Inventory]: {
        // target: 'http://localhost:8040',
        // target: domain,
        target: 'http://*************:8081',
        // target: 'http://**************:8040',
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_ImportInventory]: {
        //target: 'http://**************:8000',
        // target: domain,
        // target: 'http://**************:8140',
        target: 'http://**************:8140',
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_OpenPlatform]: {
        // target: 'http://***************:8810',
       target: domain,
       ws: true,
       changeOrigin: true,
       logLevel:'debug'
     },
      ['^' + process.env.VUE_APP_BASE_API_Warning]: {
       // target: domain+':8210',
        target: domain,
        // target: 'http://**************'+':8000',
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_OldErp]: {
        // target: domain+':8060',
        target: domain,
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_BookKeeper]: {
        //target: 'http://localhost:8090',
        target: domain,
        // target: 'http://**************:8090',
        // target: 'http://**************:8090',
        // target: 'http://*************:8081',
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_MonthBookKeeper]: {
        //target: 'http://localhost:8095',
        // target: domain,
        target: 'http://**************:8095',
        // target: 'http://*************:8081',
        ws: true,
        changeOrigin: true,
        logLevel: 'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_CustomerService]: {
        target: 'http://*************:8081',
        // target: domain,
        // target: 'http://**************:8100',
        // target:'http://localhost:8100',
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_Profit]: {
        // target: domain+':8190',
        target: domain,
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_Media]: {
        // target: domain+':8300',
        target: domain,
        //  target: 'http://**************:8300',
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_Distribution]: {
        // target: domain+':8060',
        target: domain,
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_PddPlatform]: {
         //target: domain+':8310',
        target: domain,
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_PddOperateManage]: {
        // target: 'http://**************'+':8130',
        //target: 'http://*************:31832',
        // target: domain,
        target: 'http://*************:8081',
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_PackProcess]: {
        //target: 'http://localhost:8314',
        // target: domain,
         target: 'http://**************:8314',
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_VerifyOrder]: {
        // target: 'http://**************:8150',
        // target: 'http://*************:30342',
        target: 'http://*************:8081',
        // target: 'http://127.0.0.1:8150',
        // target: domain,
        // target: 'http://*************:8081',
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_JAVA_API_BLADEGATEWAY]: {
        // target: domain,
        // target: 'http://**************:8150',
        // target: 'http://*************:30342',
        // target: 'http://*************:10012',
        target: 'http://*************:8081',
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_CwManage]: {
        //target: 'http://localhost:8160',
        // target: domain,
					target: 'http://*************:7001',
        ws: true,
        changeOrigin: true,
        logLevel: 'debug',
      },
      ['^' + process.env.VUE_APP_BASE_API_UpLoad]: {
        target:"http://*************:8070",// domain.indexOf('*************')>0?"http://*************:8070":"http://**************:8070",
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_UpLoadNew]: {
        target: 'https://nanc.yunhanmy.com:10010',
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_JAVA_API_XZGATEWAY]: {
        target: 'http://*************:9000',
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_Kj]: {
        target: domain,
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_Kjerp]: {
        // target: 'http://192.168.143.105:9004',
        target: domain,
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },
      ['^' + process.env.VUE_APP_BASE_API_WmsOperation]: {
        // target: 'http://192.168.143.86:9004',
        target: domain,
        ws: true,
        changeOrigin: true,
        logLevel:'debug'
      },

      ['^' + process.env.VUE_APP_BASE_API_TeamTask]: {
        target: process.env.VUE_APP_BASE_API_TeamTask_Domin,
        ws: true,
        changeOrigin: true,
        logLevel: 'debug',
        rewrite: (path) => path
      },
      // ['^' + process.env.VUE_APP_IMAGES_URL]: {
      //   target:"http://*************:8070",// domain.indexOf('*************')>0?"http://*************:8070":"http://**************:8070",
      //   ws: true,
      //   changeOrigin: true,
      //   logLevel:'debug'
      // },
      '^/images': {
        target: domain+':8000',
        ws: true,
        changeOrigin: true
      }
    }
    },
  transpileDependencies: [],
    configureWebpack:   {
        name: name,
        resolve: {
        alias: {
            '@': resolve('src')
        }
        },
        externals: {
            'vue': 'Vue',
            'exceljs': 'ExcelJS',
            'vxe-table': 'VXETable'
        },
        // 发布时关闭警告
        performance: { hints: false },
        plugins: [
        new CopyWebpackPlugin([{from: "src/static/excel",to: 'static/excel'}]),
        // new BundleAnalyzerPlugin(),
        ],
        optimization: {
        runtimeChunk: 'single',
        splitChunks: {
            chunks: 'all',
            maxInitialRequests: Infinity,
            minSize: 4000000,
            maxSize: 6000000,
            cacheGroups: {
                vendors: {
                    test: /[\\/]node_modules[\\/]/,
                    priority: -10,
                    name(module) {
                        const packageName = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/)[1]
                        return `npm.${packageName.replace('@', '')}`
                    },
                },
            },
        }
    }
    },
  // webpack配置
  chainWebpack: config => {
    config.plugin("define").tap((args) => {
      args[0]["process"] = { ...args[0]["process.env"] }
      return args;
    })
    config.plugins.delete('prefetch')
    config.plugins.delete('preload')
    config.when(!isDev, config => {
      config
        .plugin('ScriptExtHtmlWebpackPlugin')
        .after('html')
        .use('script-ext-html-webpack-plugin', [{
          inline: /runtime\..*\.js$/
        }])
        .end();
        config.optimization.splitChunks({
          chunks: 'all',
          cacheGroups: {
            libs: {
              name: 'chunk-libs',
              test: /[\\/]node_modules[\\/]/,
              priority: 10,
              chunks: 'initial' // only package third parties that are initially dependent
            },
            elementUI: {
              name: 'chunk-elementUI', // split elementUI into a single package
              priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
              test: /[\\/]node_modules[\\/]_?element-ui(.*)/ // in order to adapt to cnpm
            },
            commons: {
              name: 'chunk-commons',
              test: resolve('src/components'), // can customize your rules
              minChunks: 3, //  minimum common number
              priority: 5,
              reuseExistingChunk: true
            }
          }
        })
      config.optimization.runtimeChunk('single')

    })
  }
}
