<template>
    <my-container v-loading="pageLoading">
        <template #header>
            <el-button-group>
                <el-button style="padding: 0;margin: 0;border: none;width:180px;">
                    <el-select v-model.trim="filter.expertId" filterable clearable placeholder="达人ID" remote
                        reserve-keyword :remote-method="getExpertIdList" :loading="expertLoading"
                        style="width:180px">
                        <el-option v-for="item in expertIdList" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;border: none;width:140px;">
                    <el-select v-model.trim="filter.business" filterable clearable placeholder="商务"
                        style="width:140px" :disabled="businessManDisabled">
                        <el-option v-for="item in businessManList" :key="item.value" :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-button>
                <el-button style="padding: 0;margin: 0;border: none;width:140px;">
                    <el-input v-model.trim="filter.proCode" clearable placeholder="商品ID" maxlength="50">
                    </el-input>
                </el-button>
                <el-button style="padding: 0;margin: 0;border: none;width:240px;">
                    <el-input v-model.trim="filter.keywords" clearable placeholder="关键字查询 商务/达人/组/商品" maxlength="50">
                    </el-input>
                </el-button>
                <el-button type="primary" @click="onSearch">查询</el-button>
                <el-button type="primary" @click="onAddBzRef">新增达人商务商品关系</el-button>
                <el-button type="primary" @click="onExpertShow">管理达人</el-button>
                <el-button type="primary" @click="onExport" :loading="exportLoading">导出</el-button>
                <el-button type="primary" @click="onUpdateBusinessShow">批量更改商务</el-button>
                <el-button type="primary" @click="onImport">导入</el-button>
                <el-button type="primary" @click="onModel">下载模板</el-button>
            </el-button-group>
        </template>

        <vxetablebase :id="'BusinessExpert202507241621'" :border="true" :align="'center'"
            :tablekey="'BusinessExpert202507241621'" ref="table" :that='that' :isIndex='true' :hasexpand='false'
            @sortchange='sortchange' :isSelectColumn="true" @select="callback" :showsummary='true' :tablefixed='true'
            :summaryarry='summaryarry' :tableData='datalist' :tableCols='tableCols' :loading="listLoading"
            style="width:100%;height:100%;margin: 0" :xgt="9999">
        </vxetablebase>

        <!--分页-->
        <template #footer>
            <my-pagination ref="pager" :total="total" @get-page="getList" />
        </template>

        <!-- 达人管理 开始-->
        <el-dialog title="达人管理" :visible.sync="ExpertManagement.isShow" width="50%" v-dialogDrag :close-on-click-modal="false">
            <template>
                <div style="text-align: left;margin-bottom: 20px;">
                    <span style="margin-right: 2px;">达人:</span>
                    <el-button style="padding: 0;border: none;">
                        <el-input v-model.trim="ExpertManagement.expert" clearable placeholder="达人" maxlength="30"/>
                    </el-button>
                    <span style="margin-right: 2px;margin-left: 5px;">达人ID:</span>
                    <el-button style="padding: 0;border: none;">
                        <el-input v-model.trim="ExpertManagement.expertId" clearable placeholder="达人ID" maxlength="30"/>
                    </el-button>
                    <el-button type="primary" @click="onQueryExpert">查询</el-button>
                    <el-button type="primary" @click="onAddExpert">添加达人</el-button>
                    <el-button type="primary" @click="onImportExpert">导入</el-button>
                    <el-button type="primary" @click="onTemplateExpert">下载模板</el-button>
                </div>
            </template>
            <vxetablebase :id="'ExpertManagement202507241314'" :tablekey="'ExpertManagement202507241314'"
                ref="tableExpert" :that='that' :isIndex='true' :hasexpand='true' :tablefixed='true' @sortchange='sortchangeExpert'
                :tableData='tableDataExpert' :tableCols='tableColsExpert' :isSelection="false" :isSelectColumn="false"
                style="width: 100%;  margin: 0" :loading="loadingExpert"
                :height="'300'">
            </vxetablebase>
            <template #footer>
                <my-pagination v-if="ExpertManagement.isShow" ref="pagerExpert" :total="totalExpert" @page-change="PagechangeExpert" @size-change="SizechangeExpert" />
            </template>
            <el-dialog title="达人编辑" :visible.sync="isShow1" width="30%" v-dialogDrag append-to-body>
                <el-row>
                    <el-col :span="24" style="color: chocolate;">
                        提醒：点击提交后将通过达人ID更新“商务与达人关系”
                    </el-col>
                </el-row>
                <el-form :model="editExpert" ref="editExpert" :rules="editrules" label-width="100px" class="demo-ruleForm">
                    <el-form-item label="达人" prop="expert">
                        <el-input v-model.trim="editExpert.expert" clearable maxlength="30" show-word-limit/>
                    </el-form-item>
                    <el-form-item label="达人ID" prop="expertId">
                        <el-input v-model.trim="editExpert.expertId" maxlength="30" show-word-limit :disabled="true"/>
                    </el-form-item>
                </el-form>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="isShow1 = false">取 消</el-button>
                    <el-button type="primary" @click="onEditExpertData">确 定</el-button>
                </span>
            </el-dialog>
        </el-dialog>

        <el-dialog title="达人导入" :visible.sync="importExpert.isShow" width="30%" v-dialogDrag :close-on-click-modal="false">
            <div class="upload-section">
                <div class="upload-row">
                <el-upload ref="uploadExpert" class="upload-area" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :file-list="importExpert.fileListExpert" :data="importExpert.fileparmExpert" :http-request="onUploadFileExpert"
                    :on-success="onUploadSuccessExpert" :on-change="onUploadChangeExpert" :on-remove="onUploadRemoveExpert">
                    <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button size="small" type="success" :loading="importExpert.uploadLoading" @click="onSubmitUploadExpert" class="upload-btn">
                    {{ importExpert.uploadLoading ? '上传中' : '上传' }}
                    </el-button>
                </el-upload>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="importExpert.isShow = false">关闭</el-button>
            </span>
        </el-dialog>
        <!-- 达人管理 结束 -->

        <!-- 新增达人商务商品关系 -->
        <el-dialog :title="addEdit.title" :visible.sync="isShow2" width="25%" v-dialogDrag>
            <el-form :model="addEditForm" ref="addEditForm" :rules="addEditFormRules" label-width="100px" class="demo-ruleForm">
                <!-- <el-form-item label="达人ID" prop="expertId">
                    <el-select v-model.trim="addEditForm.expertId" filterable clearable placeholder="达人ID" remote
                        reserve-keyword :remote-method="getExpertIdList" :loading="expertLoading"
                        style="width:180px">
                        <el-option v-for="item in expertIdList" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item> -->
                <el-form-item label="商品ID" prop="proCode">
                    <el-input v-model.trim="addEditForm.proCode" clearable  show-word-limit/>
                </el-form-item>
                <!-- <el-form-item label="商务" prop="business">
                    <el-select v-model.trim="addEditForm.business" filterable clearable placeholder="商务">
                        <el-option v-for="item in businessManList" :key="item.value" :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item> -->
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="isShow2 = false">取 消</el-button>
                <el-button type="primary" @click="onaddEditFormData" :loading="addEdit.loading">确 定</el-button>
            </span>
        </el-dialog>
        <!-- 达人商务商品关系批量修改 -->
        <el-dialog :title="batchEdit.title" :visible.sync="batchEdit.isShow" width="25%" v-dialogDrag>
            <el-form :model="batchForm" ref="batchForm" label-width="100px" class="demo-ruleForm">
                <el-form-item label="商务" prop="business">
                    <el-select v-model.trim="batchForm.business" filterable clearable placeholder="商务">
                        <el-option v-for="item in businessManList" :key="item.value" :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="batchEdit.isShow = false">取 消</el-button>
                <el-button type="primary" @click="onBatchFormData" :loading="batchEdit.loading">确 定</el-button>
            </span>
        </el-dialog>
        <!-- 达人商务商品关系导入 -->
        <el-dialog title="达人商务商品关系导入" :visible.sync="importBusiness.isShow" width="30%" v-dialogDrag :close-on-click-modal="false">
            <div class="upload-section">
                <div class="upload-row">
                <el-upload ref="uploadBusiness" class="upload-area" :auto-upload="false" :multiple="false" :limit="1" action
                    accept=".xlsx" :file-list="importBusiness.fileListBusiness" :data="importBusiness.fileparmBusiness" :http-request="onUploadFileBusiness"
                    :on-success="onUploadSuccessBusiness" :on-change="onUploadChangeBusiness" :on-remove="onUploadRemoveBusiness">
                    <template #trigger>
                    <el-button size="small" type="primary">选取文件</el-button>
                    </template>
                    <el-button size="small" type="success" :loading="importBusiness.uploadLoading" @click="onSubmitUploadBusiness" class="upload-btn">
                    {{ importBusiness.uploadLoading ? '上传中' : '上传' }}
                    </el-button>
                </el-upload>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="importBusiness.isShow = false">关闭</el-button>
            </span>
        </el-dialog>
    </my-container>
</template>
<script>
import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
import cesTable from "@/components/Table/table.vue";
import MyContainer from "@/components/my-container";
import MyConfirmButton from "@/components/my-confirm-button";
import MySearch from "@/components/my-search";
import MySearchWindow from "@/components/my-search-window";
import { downloadLink, formatLinkProCode } from "@/utils/tools.js";
import { GetAllBusinessDetailList } from "@/api/bookkeeper/reportdayDouYin";
import { GetVideoExpertManagementList, AddEditVideoExpertManagement, DeleteVideoExpertManagement, ImportVideoExpertManagement,
    GetVideoBusinessExpertList, AddEditVideoBusinessExpert, BatchEditVideoBusinessExpert, ExportVideoBusinessExpert, ImportVideoBusinessExpert
} from "@/api/bookkeeper/reportdayV2"

const tableCols = [
    { istrue: true, label: '', type: "checkbox",},
    { istrue: true, label: '小组头像',  width: '70',type:'ddAvatar',ddInfo:{type:1,prop:'groupId'} },
    { istrue: true, prop: 'groupName', label: '运营组', sortable: 'custom', width: '80',type:'ddTalk',ddInfo:{type:1,prop:'groupId',name:'groupName'}, },
    { istrue: true, prop: 'business', label: '商务', sortable: 'custom', width: '130' },
    { istrue: true, prop: 'expert', label: '达人', sortable: 'custom', width: '170' },
    { istrue: true, prop: 'expertId', label: '达人ID', sortable: 'custom', width: '150' },
    { istrue: true, align:'center', prop: 'proCode', label: '商品ID', sortable: 'custom', width: '150',type:'html',formatter:(row) => formatLinkProCode(20,row.proCode,) },
    { istrue: true, prop: 'proName', label: '商品名称', sortable: 'custom' },
    { istrue: true, prop: 'createdTime', label: '添加时间', sortable: 'custom', width: '150' },
    { istrue: true, prop: 'editTime', label: '修改时间', sortable: 'custom', width: '150' },
    {
        istrue: true, label: '功能', width: '100', type: 'button', fixed: 'right', align: 'center', btnList: [
            {
                label: '编辑',
                handle: (that, row) => that.onEditBzRef(row),
            },
        ]
    }
];
const tableColsExpert = [
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'expert', label: '达人' },
    { sortable: 'custom', width: 'auto', align: 'left', prop: 'expertId', label: '达人ID' },
    {
        istrue: true, type: "button", label: '操作', width: "120",
        btnList: [
            { label: "编辑", handle: (that, row) => that.onEditExpert(row) },
            { label: "删除", handle: (that, row) => that.deleteExpert(row) }
        ]
    }
]

export default {
    name: "BusinessExpert",
    components: {
        MyContainer, MyConfirmButton, MySearch, MySearchWindow, cesTable,
        vxetablebase
    },
    data() {
        return {
            that: this,
            filter:{
                expertId: null,
                business: null,
                proCode: null,
                keywords: null,
            },
            businessManList: [],
            tableCols,
            datalist:null,
            summaryarry: null,
            listLoading: false,
            pager: { OrderBy: "", IsAsc: false },
            addEdit:{
                title: '',
                loading: false
            },
            isShow2: false,
            addEditForm:{
                id : 0,
                expertId: null,
                proCode: null,
                business: null,
            },
            addEditFormRules: {
                expertId: [{ required: true, message: '请输入达人ID', trigger: 'blur' }],
                proCode: [{ required: true, message: '请输入商品ID', trigger: 'blur' }],
            },
            batchEdit:{
                title: '批量修改商务',
                isShow: false,
                loading: false
            },
            selrows: [],
            batchForm: {
                ids: [],
                business: null,
            },
            exportLoading: false,
            importBusiness:{
                isShow: false,
                fileListBusiness: [],
                fileparmBusiness: {},
                uploadLoading: false
            },
            expertLoading: false,
            expertIdList: [],
            ExpertManagement:{
                isShow: false,
                id: 0,
                expert: null,
                expertId: null,
                orderBy: 'createdTime',
                isAsc: false,
                currentPage: 1,
                pageSize: 50
            },
            loadingExpert: false,
            tableColsExpert,
            tableDataExpert: null,
            totalExpert: 0,
            isShow1: false,
            editExpert:{
                expert: null,
                expertId: null,
                id: 0,
            },
            editrules: {
                expert: [{ required: true, message: '请输入达人', trigger: 'blur' }],
            },
            importExpert:{
                isShow: false,
                fileListExpert: [],
                uploadLoading: false,
                fileparmExpert: {}
            }
        };
    },
    mounted() {
        this.onSearch();
    },
    created() {
        this.getBusinessNameList();
        this.getExpertIdList();
    },
    methods: {
        //获取商务
        async getBusinessNameList() {
            const resBusiness = await GetAllBusinessDetailList();
            this.businessManList = resBusiness.data?.map(item => { return { value: item.businessMan, label: item.businessMan }; });
        },
        //搜索
        async onSearch() {
            this.$refs.pager.setPage(1);
            await this.getList();
        },
        //获取列表
        async getList() {
            var that = this;
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };

            that.listLoading = true;
            const res = await GetVideoBusinessExpertList(params);
            that.total = res.data?.total;
            that.datalist = res.data?.list;
            that.summaryarry = res.data?.summary;
            that.listLoading = false;
        },
        //排序
        sortchange(column) {
            if (!column.order)
                this.pager = {};
            else
                this.pager = { OrderBy: column.prop, IsAsc: column.order.indexOf("descending") == -1 ? true : false }
            this.onSearch();
        },
        //新增达人商务关联
        onAddBzRef(){
            this.addEditForm = {
                id : 0,
                expertId: '',
                proCode: '',
                business: '',
            };
            this.addEdit.title = '新增达人商务商品关系';
            this.isShow2 = true;
        },
        //修改达人商务关联
        onEditBzRef(row){
            this.getExpertIdList(row.expertId);
            this.addEditForm = {
                id : row.id,
                expertId: row.expertId,
                proCode: row.proCode,
                business: row.business,
            };
            this.addEdit.title = '编辑';
            this.isShow2 = true;
            console.log(this.businessManList,'businessManList');
            console.log(this.expertIdList,'expertIdList');
            console.log(this.isShow2,'111111111111111111111111111111111111111111111111')
        },
        async onaddEditFormData(){
            if(!this.addEditForm.expertId || !this.addEditForm.proCode){
                this.$message.warning("请将信息填写完整！")
                return;
            }
            this.addEdit.loading = true;
            const res = await AddEditVideoBusinessExpert(this.addEditForm)
            this.addEdit.loading = false;
            if(res.success){
                if(this.addEdit.title == '编辑'){
                    this.$message.success("编辑成功！")
                }else if(this.addEdit.title == '新增达人商务商品关系'){
                    this.$message.success("新增成功！")
                }
                this.isShow2 = false
                this.getList()
            }
        },
        //批量修改弹窗
        onUpdateBusinessShow(){
            if (this.selrows.length <= 0) {
                this.$message({ type: 'error', message: '请勾选!' });
                return;
            }
            this.batchForm.business = null;
            this.batchForm.ids = [];
            this.batchEdit.isShow = true;
        },
        callback(val) {
            this.selrows = [...val];
            console.log(val)
        },
        async onBatchFormData(){
            this.batchEdit.loading = true;
            this.selrows.forEach(f => {
                this.batchForm.ids.push(f.id)
            })
            const res = await BatchEditVideoBusinessExpert(this.batchForm)
            this.batchEdit.loading = false;
            if(res.success){
                this.batchForm = { ids : null, business: null };
                this.$message.success("修改成功！")
                this.selrows = []
                this.batchEdit.isShow = false
                this.getList()
            }
        },
        //导出
        async onExport() {
            var pager = this.$refs.pager.getPager();
            const params = { ...pager, ...this.pager, ...this.filter };
            this.exportLoading = true;
            var res = await ExportVideoBusinessExpert(params);
            this.exportLoading = false;
            if (!res?.data) {
                return false;
            };
            const aLink = document.createElement("a");
            let blob = new Blob([res.data], { type: "application/vnd.ms-excel" })
            aLink.href = URL.createObjectURL(blob)
            aLink.setAttribute('download', '视频号商务达人导出' + new Date().toLocaleString() + '_.xlsx')
            aLink.click()
        },
        onImport(){
            this.importBusiness = {
                isShow: true,
                fileListBusiness: [],
                fileparmBusiness: {},
                uploadLoading: false
            }
        },
        onUploadRemoveBusiness(file, fileList) {
            this.importBusiness.fileListBusiness= []
        },
        onUploadChangeBusiness(file, fileList) {
            this.importBusiness.fileListBusiness = fileList;
        },
        onUploadSuccessBusiness(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.importBusiness.fileListBusiness = [];
            this.importBusiness.isShow = false;
        },
        onSubmitUploadBusiness() {
            if (this.importBusiness.fileListBusiness.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.uploadBusiness.submit();
        },
        //提交导入
        async onUploadFileBusiness(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.importBusiness.uploadLoading = true
            const form = new FormData();
            form.append("upfile", item.file);
            var res = await ImportVideoBusinessExpert(form);
            if (res?.success)
                this.$message({ message: res.message || "上传成功,正在导入中...", type: "success" });
            this.importBusiness.uploadLoading = false
            this.importBusiness.isShow = false;
            await this.getList()
        },
        //下载商务达人导入模板
        onModel() {
            downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250725/1948673404517720064.xlsx', '商务达人导入模板.xlsx');
        },
        //获取达人ID
        async getExpertIdList(value){
            const res = await GetVideoExpertManagementList({ expertId: value, currentPage: 1, pageSize: 20, OrderBy:'createdTime', IsAsc:false });
            this.expertIdList = res.data.list?.map(item => {
                return { value: item.expertId, label: item.expert + '（达人ID:' + item.expertId + '）' };
            });
        },
        //打开达人管理
        onExpertShow(){
            this.ExpertManagement = {
                isShow : true,
                expert: null,
                expertId: null,
                orderBy: 'createdTime',
                isAsc: false,
                pageSize:50,
                currentPage: 1
            }
            this.onQueryExpert()
        },
        //查询达人管理页面
        async onQueryExpert(){
            this.loadingExpert = true
            const params = {
                ...this.ExpertManagement
            }
            const { data, success } = await GetVideoExpertManagementList(params)
            this.loadingExpert = false;
            if(success){
                this.tableDataExpert = data.list
                this.totalExpert = data.total
            }else{
                this.$message.error('获取列表失败')
            }
        },
        //添加达人
        async onAddExpert(){
            if(this.ExpertManagement.expert == undefined || this.ExpertManagement.expertId == undefined || this.ExpertManagement.expert == "" || this.ExpertManagement.expertId == ""){
                this.$message.error('请先将信息填写完整！')
                return;
            }
            const params = {
                expert: this.ExpertManagement.expert,
                expertId: this.ExpertManagement.expertId
            }
            const res = await AddEditVideoExpertManagement(params)
            if(res.success){
                this.ExpertManagement.expert = null;
                this.ExpertManagement.expertId = null;
                this.$message.success("添加成功！")
                this.onQueryExpert()
            }
        },
        //编辑达人
        onEditExpert(row){
            this.editExpert.id = row.id
            this.editExpert.expert = row.expert
            this.editExpert.expertId = row.expertId
            this.isShow1 = true
        },
        //提交编辑后的达人
        async onEditExpertData(){
            if(!this.editExpert.expertId || !this.editExpert.expert){
                this.$message.warning("请将信息填写完整！")
                return;
            }
            const res = await AddEditVideoExpertManagement(this.editExpert)
            if(res.success){
                this.$message.success("编辑成功！")
            }
            this.isShow1 = false
            this.onQueryExpert()
        },
        //删除达人
        async deleteExpert(row){
            this.$confirm('此操作将删除此条数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                const params = { ...row, deleteType: 0 }
                DeleteVideoExpertManagement(params).then(res => {
                if (res.success) {
                    this.$message({
                    type: 'success',
                    message: '删除成功!'
                    })
                    this.onQueryExpert()
                }
                }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                })
                })
            })
        },
        //下载达人管理导入模板
        onTemplateExpert() {
            downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250724/1948267117720084481.xlsx', '达人管理导入模板.xlsx');
        },
        //打开达人导入
        onImportExpert(){
            this.importExpert={
                isShow: true,
                fileListExpert: [],
                uploadLoading: false,
                fileparmExpert: {}
            }
        },
        onUploadRemoveExpert(file, fileList) {
            this.importExpert.fileListExpert= []
        },
        onUploadChangeExpert(file, fileList) {
            this.importExpert.fileListExpert = fileList;
        },
        onUploadSuccessExpert(response, file, fileList) {
            fileList.splice(fileList.indexOf(file), 1);
            this.importExpert.fileListExpert = [];
            this.importExpert.isShow = false;
        },
        onSubmitUploadExpert() {
            if (this.importExpert.fileListExpert.length == 0) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.$refs.uploadExpert.submit();
        },
        //提交导入
        async onUploadFileExpert(item) {
            if (!item || !item.file || !item.file.size) {
                this.$message({ message: "请先上传文件", type: "warning" });
                return false;
            }
            this.importExpert.uploadLoading = true
            const form = new FormData();
            form.append("upfile", item.file);
            var res = await ImportVideoExpertManagement(form);
            if (res?.success)
                this.$message({ message: res.data || "上传成功,正在导入中...", type: "success" });
            this.importExpert.uploadLoading = false
            this.importExpert.isShow = false;
            await this.onQueryExpert()
        },
        //排序
        sortchangeExpert({ order, prop }) {
            if (prop) {
                this.ExpertManagement.orderBy = prop
                this.ExpertManagement.isAsc = order.indexOf("descending") == -1 ? true : false
                this.onQueryExpert()
            }
        },
        //达人每页数量改变
        SizechangeExpert(val) {
            this.ExpertManagement.currentPage = 1;
            this.ExpertManagement.pageSize = val;
            this.onQueryExpert()
        },
        //达人当前页改变
        PagechangeExpert(val) {
            this.ExpertManagement.currentPage = val;
            this.onQueryExpert()
        },
    }
};
</script>
