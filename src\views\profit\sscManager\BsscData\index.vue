<template>
    <MyContainer>
        <template #header>
        <div class="top">
          <el-date-picker v-model="ListInfo.calculateMonthArr" unlink-panels range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期"   type="monthrange" style="width: 250px;margin-right: 5px;" :clearable="false"
            :value-format="'yyyy-MM'" >
          </el-date-picker>

          <!-- <el-select v-model="ListInfo.isStock" placeholder="业务部门" class="publicCss" clearable>
            <el-option :key="'是'" label="是" :value="0" />
            <el-option :key="'否'" label="否" :value="1" />
          </el-select> -->
        <el-select v-model="ListInfo.regionNameArr" placeholder="区域" class="publicCss" clearable collapse-tags multiple>
          <el-option v-for="item in districtRegionList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="ListInfo.deptName" placeholder="部门类型" class="publicCss" clearable>
          <el-option v-for="item in districtList" :key="item" :label="item" :value="item" />
        </el-select>
        <el-select v-model="ListInfo.dept" placeholder="部门" class="publicCss" clearable>
          <el-option v-for="item in districtNameList" :key="item" :label="item" :value="item" />
        </el-select>

          <el-button type="primary" @click="getList('search')">查询</el-button>
          <!-- <el-button type="primary" @click="startImport" v-if="checkPermission('ArchiveStatusEditing')">导入</el-button>
          <el-button type="primary" @click="startImport" v-else :disabled="!timeCundang">导入</el-button> -->
          <el-button type="primary" @click="startImport" v-if="checkPermission('ArchiveStatusEditing')">导入</el-button>
          <el-button type="primary" @click="startImport" v-else :disabled="timeCundang">导入</el-button>
          <el-button type="primary" @click="downExcel">模板下载</el-button>
          <el-button type="primary" @click="exportExcel('search')">导出</el-button>
          <el-button type="primary" @click="saveBane('search')">存档</el-button>
          <div style="color: red; margin-left: 5px;">
            存档时间：{{timeCundang??'-'}}
          </div>

        </div>
      </template>
      <vxe-table
        border
        ref="newtable"
        show-footer
        :loading="loading"
        height="100%"
        :row-config="{height: 40}"
        show-overflow
        :footer-data="footerData"
        :span-method="mergeRowMethod"
        :footer-span-method="footerSpanMethod"
        :row-class-name="rowClassName"
        :cell-class-name="cellClassName"
        :footer-cell-class-name="footerCellClassName"
        :footer-row-class-name="footerRowClassName"
        :column-config="{resizable: true}"
        :data="tableData">
        <vxe-column field="calculateMonth" title="月份" footer-align="center">
          <template #footer="{ items, _columnIndex, row }">
            <span v-if="mergeColumn?.column?.includes('calculateMonth')" class="display_centered">
              {{ getMergeDisplayValue(row) }}
            </span>
            <span v-else>{{ items[_columnIndex] }}</span>
          </template>
        </vxe-column>
        <vxe-column field="type" title="类型" footer-align="center">
          <template #footer="{ items, _columnIndex, row }">
            <span v-if="mergeColumn?.column?.includes('type')" class="display_centered">
              {{ getMergeDisplayValue(row) }}
            </span>
            <span v-else>{{ items[_columnIndex] }}</span>
          </template>
        </vxe-column>

        <vxe-column field="regionName" title="区域" footer-align="center">
          <template #footer="{ items, _columnIndex, row }">
            <span v-if="mergeColumn?.column?.includes('regionName')" class="display_centered">
              {{ getMergeDisplayValue(row) }}
            </span>
            <span v-else>{{ items[_columnIndex] }}</span>
          </template>
        </vxe-column>
        <vxe-column field="deptName" title="部门类型" footer-align="center">
          <template #footer="{ items, _columnIndex, row }">
            <span v-if="mergeColumn?.column?.includes('deptName')" class="display_centered">
              {{ getMergeDisplayValue(row) }}
            </span>
            <span v-else>{{ items[_columnIndex] }}</span>
          </template>
        </vxe-column>
        <vxe-column field="dept" title="部门" footer-align="center">
          <template #footer="{ items, _columnIndex, row }">
            <span v-if="mergeColumn?.column?.includes('dept')" class="display_centered">
              {{ getMergeDisplayValue(row) }}
            </span>
            <span v-else>{{ items[_columnIndex] }}</span>
          </template>
        </vxe-column>
        <vxe-column field="onpostPersonCount" title="在职人数" ></vxe-column>


        <vxe-colgroup title="异动情况" width="150">
            <vxe-column field="regularPersonCount" title="转正人数" footer-align="left"></vxe-column>
            <vxe-column field="promotionPersonCount" title="晋升人数" footer-align="left"></vxe-column>
            <vxe-column field="salarychangePersonCount" title="调薪人数" footer-align="left"></vxe-column>
            <vxe-column field="postchangePersonCount" title="调岗人数" footer-align="left"></vxe-column>
            <vxe-column field="changesPersonCount" title="异动合计" footer-align="left"></vxe-column>
            <vxe-column field="abnormalProportion" title="异动比例" footer-align="left">
              <template #default="{ row }">
                {{row.abnormalProportion  ?(row.abnormalProportion).toFixed(0)+"%": '0%'}}
              </template>
            </vxe-column>
        </vxe-colgroup>

        <vxe-colgroup title="五险一金缴纳情况" width="150">
            <vxe-column field="insuredPersonCount" title="参保人数" footer-align="left"></vxe-column>
            <vxe-column field="insuredRate" title="参保占比" footer-align="left">
                <template #default="{ row }">
                {{row.insuredRate  ?(row.insuredRate).toFixed(0)+"%": '0%'}}
                </template>
            </vxe-column>
            <vxe-column field="socialSecurityCost" title="企业社保费用" width="130" footer-align="left"></vxe-column>
            <vxe-column field="socialProvidentCost" title="企业公积金费用" footer-align="left" width="130"></vxe-column>
        </vxe-colgroup>

        <!-- <vxe-column field="entryPersonCount" title="入职人数" footer-align="left"></vxe-column> -->
        <vxe-column title="操作" footer-align="left">
            <template slot-scope="scope">
              <el-button type="text" size="mini" v-if="checkPermission('ArchiveStatusEditing')&&scope.row.deptName.indexOf('小计')==-1" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
              <el-button type="text" size="mini" style="color: red;" :disabled="scope.row.status==1" v-if="scope.row.deptName.indexOf('小计')==-1" @click="handleRemove(scope.$index, scope.row)">删除</el-button>
              <el-button type="text" size="mini" :disabled="scope.row.status==1" v-else-if="scope.row.deptName.indexOf('小计')==-1" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
              <!-- <el-button type="text" style="color: red" size="mini" @click="handleDelete(scope.$index, scope.row)">删除</el-button> -->
            </template>
        </vxe-column>
      </vxe-table>
      <template #footer>
        <div class="footer-container">
          <span class="total-count-badge">
            共{{ total || 0 }}条
          </span>
        </div>
      </template>
      <!-- <template #footer>
        <my-pagination ref="pager" :total="total" @page-change="Pagechange" @size-change="Sizechange" />
      </template> -->

      <el-drawer title="编辑" :visible.sync="dialogVisibleEdit" size="25%">
        <departmentEdit ref="departmentEdit" v-if="dialogVisibleEdit" :editInfo="editInfo" @search="closeGetlist"
            @cancellationMethod="dialogVisibleEdit = false" />
      </el-drawer>

      <el-dialog title="导入数据" :visible.sync="dialogVisible" width="30%" v-dialogDrag :close-on-click-modal="false">
        <span>
            <el-upload ref="upload" class="upload-demo" :auto-upload="false" :multiple="false" :limit="1" action
            accept=".xlsx" :file-list="fileList" :data="fileparm" :http-request="onUploadFile"
            :on-success="onUploadSuccess" :on-change="onUploadChange" :on-remove="onUploadRemove">
            <template #trigger>
                <el-button size="small" type="primary">选取文件</el-button>
            </template>
            <el-button style="margin-left: 10px" size="small" type="success" :loading="uploadLoading"
                @click="onSubmitUpload">{{ (uploadLoading ? '上传中' : '上传') }}</el-button>
            </el-upload>
        </span>
        <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
        </el-dialog>
    </MyContainer>
  </template>

  <script>
  import MyContainer from "@/components/my-container";
  import vxetablebase from "@/components/VxeTable/yh_vxetable.vue";
  import { pickerOptions } from '@/utils/tools';
  import departmentEdit from "./departmentEdit.vue";
  import { downloadLink } from "@/utils/tools.js";
  import dayjs from 'dayjs'
  import checkPermission from '@/utils/permission'
  import { sscDataPage, sscDataArchive, sscDataImport,sscDataRemove } from '@/api/people/peoplessc.js';
  import tableFooterMerge from "@/views/profit/sscManager/tableFooterMerge.js";
  const tableCols = [
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNo', label: '审批状态', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderNoInner', label: '商品编码', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'importTime', label: '商品名称', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'planSendTime', label: '原价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'timePay', label: '现价', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'weight', label: '进货数量', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'orderStatus', label: '涨价原因', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'seriesName', label: '添加日期', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'goodsCount', label: '涨价日期', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '添加人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '岗位', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '分公司', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '审批人', },
    { sortable: 'custom', width: 'auto', align: 'center', prop: 'totalQuantity', label: '审批人组长', },
  ]
  export default {
    name: "scanCodePage",
    mixins: [tableFooterMerge],
    components: {
      MyContainer, vxetablebase, departmentEdit
    },
    data() {
        const tableData = [
    //   { id: 10001,regionName: '义务', deptName: 'it部门', nickname: 'T1', role: 'Develop', regularCount: 'Man', probationCount: 28, address: 'test abc' },
    //   { id: 10002,regionName: '义务', deptName: '人事部', nickname: 'T2', role: 'Test', regularCount: 'Women', probationCount: 22, address: 'Guangzhou' },
    //   { id: 10002,regionName: '义务', deptName: '小计', nickname: 'T2', role: 'Test', regularCount: 'Women', probationCount: 22, address: 'Guangzhou' },
    //   { id: 10003,regionName: '南昌', deptName: '运营部', nickname: 'T3', role: 'PM', regularCount: 'Man', probationCount: 32, address: 'Shanghai' },
    //   { id: 10004,regionName: '南昌', deptName: 'it部门', nickname: 'T4', role: 'Designer', regularCount: 'Women', probationCount: 23, address: 'test abc' },
    //   { id: 10002,regionName: '南昌', deptName: '小计', nickname: 'T2', role: 'Test', regularCount: 'Women', probationCount: 22, address: 'Guangzhou' },
    //   { id: 10005,regionName: '北京', deptName: '人事部', nickname: 'T5', role: 'Develop', regularCount: 'Women', probationCount: 30, address: 'Shanghai' },
    //   { id: 10006,regionName: '北京', deptName: '运营部', nickname: 'T6', role: 'Designer', regularCount: 'Women', probationCount: 21, address: 'test abc' },
    //   { id: 10002,regionName: '北京', deptName: '小计', nickname: 'T2', role: 'Test', regularCount: 'Women', probationCount: 22, address: 'Guangzhou' },
    //   { id: 10007,regionName: '深圳', deptName: 'it部门', nickname: 'T7', role: 'Test', regularCount: 'Man', probationCount: 29, address: 'test abc' },
    //   { id: 10008,regionName: '深圳', deptName: '人事部', nickname: 'T8', role: 'Develop', regularCount: 'Man', probationCount: 35, address: 'test abc' },
    //   { id: 10002,regionName: '深圳', deptName: '小计', nickname: 'T2', role: 'Test', regularCount: 'Women', probationCount: 22, address: 'Guangzhou' },
    ]
    const footerData = [
    //   {regionName: '办公室合计', deptName: '办公室合计', role: '33', rate: '56' },
    //   {regionName: '仓储合计', deptName: '仓储合计', role: 'bb', rate: '56' },
    //   {regionName: '全区域合计', deptName: '全区域合计', role: 'bb', rate: '1235' }
    ]
    const mergeFooterItems = [
      { row: 0, col: 0, rowspan: 1, colspan: 2 },
      { row: 1, col: 0, rowspan: 1, colspan: 2 },
      { row: 2, col: 0, rowspan: 1, colspan: 2 }
    ]
//     const lastMonth = dayjs().subtract(1, 'month');
//   this.ListInfo.startTime = lastMonth.format('YYYY-MM');
      return {
        downloadLink,
        dialogVisible: false,
        fileList: [],
        districtList: [],
        districtNameList: [],
        districtRegionList: [],
        regionNameArr: [],
        timeCundang: '',
        tableData,
        footerData,
        mergeFooterItems,
        somerow: 'type,regionName,calculateMonth',
        mergeColumn: {
          column: ['calculateMonth', 'type', 'regionName', 'deptName', 'dept'], // 需要合并的列
          default: '' // 默认显示字段
        },
        dialogVisibleEdit: false,
        that: this,
        editInfo: {},
        ListInfo: {
            calculateMonthArr: [dayjs().subtract(0, 'month').format('YYYY-MM'), dayjs().subtract(0, 'month').format('YYYY-MM')]
        //   currentPage: 1,
        //   pageSize: 50,
        //   orderBy: null,
        //   isAsc: false,
        //   startTime: null,//开始时间
        //   endTime: null,//结束时间
        },
        timeRanges: [],
        tableCols,
        summaryarry: {},
        total: 0,
        loading: false,
        pickerOptions,
      }
    },
    async mounted() {
      await this.getList()
    },
    methods: {
        //上传文件
        onUploadRemove(file, fileList) {
        this.fileList = []
        },
        async onUploadChange(file, fileList) {
        this.fileList = fileList;
        },
        onUploadSuccess(response, file, fileList) {
        fileList.splice(fileList.indexOf(file), 1);
        this.fileList = [];
        this.dialogVisible = false;
        },
        async onUploadFile(item) {
        if (!item || !item.file || !item.file.size) {
            this.$message({ message: "请先上传文件", type: "warning" });
            return false;
        }
        this.uploadLoading = true
        const form = new FormData();
        form.append("file", item.file);
        // form.append("calculateMonth", this.ListInfo.calculateMonth);
        form.append("isArchive", checkPermission("ArchiveStatusEditing"));
        var res = await sscDataImport(form);
        if (res?.success){
            this.$message({ message: res.msg, type: "success" });

        }
        this.uploadLoading = false
        this.dialogVisible = false;
        await this.getList()
        },
        onSubmitUpload() {
        if (this.fileList.length == 0) {
            this.$message({ message: "请先上传文件", type: "warning" });
            return false;
        }
        this.$refs.upload.submit();
        },
        //导入弹窗
        startImport() {
        this.fileList = []
        this.dialogVisible = true;
        },
        downExcel(){
            downloadLink('https://nanc.yunhanmy.com:10010/media/video/20250712/1943849906557394945.xlsx', 'ssc异动数据导入模板.xlsx');
        },
        async saveBane(){
              this.$confirm('是否存档？存档后不可修改！', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(async () => {
                    const { data, success } = await sscDataArchive(this.ListInfo)
                    if(!success){
                        return;
                    }
                    this.getList();
                    this.$message.success('保存存档成功！')

                }).catch(() => {
                    // this.$message.error('取消')
                });


        },
        closeGetlist(){
            this.dialogVisibleEdit = false;
            this.getList()
        },
        handleEdit(index, row){
            this.dialogVisibleEdit = true;
            this.editInfo = row;
        },
        async handleRemove(index, row) {
        this.$confirm('是否删除！', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          this.editInfo = row;
          this.editInfo.calculateMonthStart = this.ListInfo.calculateMonthArr[0]
          this.editInfo.calculateMonthEnd = this.ListInfo.calculateMonthArr[1]
          this.loading = true
          const {data, success} = await sscDataRemove(this.editInfo)
          this.loading = false
          if (success) {
            this.$message.success('删除成功')
            this.getList();
          } else {
            this.$message.error('删除失败')
          }
        }).catch(() => {

        });


      },
        exportExcel(){
            // this.$refs.newtable.exportData({filename:'ssc异动数据',    sheetName: 'Sheet1',type: 'xlsx' })
            this.$refs.newtable.exportData({filename:'ssc异动数据',    sheetName: 'Sheet1',type: 'xlsx' })

        },
        footerCellClassName(event){
            // console.log("=====--", event)

            // if(event.row.regionName.indexOf){
            //     return 'row-green'
            // }
            return null
        },
        footerRowClassName(event){
            if(event.row.deptName=='全区域合计'){
                return 'row-bagreen'
            }else{
                return 'row-green'
            }
            return null
        },
        rowClassName (event) {
            if(event.row.deptName == '小计'){
                return 'row-green'
            }
            return null
        },
        cellClassName ({ row, column }) {
        if (column.field === 'regularCount') {
            if (row.probationCount <= 26) {
            return 'col-red'
            } else if (row.probationCount > 26) {
            return 'col-orange'
            }
        }
        return null
        },
        // 通用行合并函数（将相同多列数据合并为一行）
        mergeRowMethod ({ row, _rowIndex, column, visibleData }) {
            const fields = this.somerow.split(',')
            const cellValue = row[column.property]
            if (cellValue && fields.includes(column.property)) {
            const prevRow = visibleData[_rowIndex - 1]
            let nextRow = visibleData[_rowIndex + 1]
            if (prevRow && prevRow[column.property] === cellValue) {
                return { rowspan: 0, colspan: 0 }
            } else {
                let countRowspan = 1
                while (nextRow && nextRow[column.property] === cellValue) {
                nextRow = visibleData[++countRowspan + _rowIndex]
                }
                if (countRowspan > 1) {
                return { rowspan: countRowspan, colspan: 1 }
                }
            }
            }
        },

      async changeTime(e) {
        this.ListInfo.startTime = e ? e[0] : null
        this.ListInfo.endTime = e ? e[1] : null
      },
      //导出数据,使用时将下面的方法替换成自己的接口
      // async exportProps() {
      //     const { data } = await exportStatData(this.ListInfo)
      //     const aLink = document.createElement("a");
      //     let blob = new Blob([data], { type: "application/vnd.ms-excel" })
      //     aLink.href = URL.createObjectURL(blob)
      //     aLink.setAttribute('download', '汇总数据' + new Date().toLocaleString() + '.xlsx')
      //     aLink.click()
      // },
      async getList(type) {
        // if (type == 'search') {
        //   this.ListInfo.currentPage = 1
        //   this.$refs.pager.setPage(1)
        // }
        if (this.ListInfo.calculateMonthArr && this.ListInfo.calculateMonthArr.length > 0) {
          //默认给近7天时间
        //   this.ListInfo.startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD')
        //   this.ListInfo.endTime = dayjs().format('YYYY-MM-DD')
        //   this.timeRanges = [this.ListInfo.startTime, this.ListInfo.endTime]
            this.ListInfo.calculateMonthStart = this.ListInfo.calculateMonthArr[0]
            this.ListInfo.calculateMonthEnd = this.ListInfo.calculateMonthArr[1]
        }

        this.ListInfo.regionName = this.ListInfo.regionNameArr.join(',');

        this.loading = true
        const { data, success } = await sscDataPage(this.ListInfo)
        if (success) {
            console.log(data)
          this.tableData = data.list
          this.footerData = data.summary;
          const fieldsToFormat = [
            "onpostPersonCount",
            "entryPersonCount",
            "regularPersonCount",
            "promotionPersonCount",
            "salarychangePersonCount",
            "postchangePersonCount",
            "changesPersonCount",
            "insuredPersonCount",
            "socialSecurityCost",
          ];
          this.footerData.forEach((item) => {
            fieldsToFormat.forEach((field) => {
              if (item[field] !== null && item[field] !== undefined) {
                item[field] = this.formatNumberWithThousandSeparator(item[field]);
              }
            });
          });
        this.footerData.forEach((item) => {
          ['insuredRate', 'abnormalProportion'].forEach((key) => {
            if (item[key] !== null && item[key] !== undefined) {
              item[key] = `${Math.round(Number(item[key]))}%`;
            }
          });
        });
        //   this.timeCundang = data.list.length>0?data.list[0].archiveTime:''
        // this.timeCundang = data.summary.archiveTime
        this.timeCundang = data.summary[0].archiveTime

           //取列表中的区域
        const newDistricts = this.tableData.map(item => item.deptName).filter(district => district !== undefined && district !== null && district != '小计')
        const newDistrictsRegion = this.tableData.map(item => item.regionName).filter(district => district !== undefined && district !== null && district != '小计')
        const newDistrictsName = this.tableData.map(item => item.dept).filter(district => district !== undefined && district !== null && district != '小计')
        this.districtList = Array.from(new Set([...this.districtList, ...newDistricts]));
        this.districtRegionList = Array.from(new Set([...this.districtRegionList, ...newDistrictsRegion]));
        this.districtNameList = Array.from(new Set([...this.districtNameList, ...newDistrictsName]));

        this.total = data.total
        //   this.summaryarry = data.summary
        // 确保表格重新渲染以应用合并功能
        this.$nextTick(() => {
          if (this.$refs.newtable) {
            this.$refs.newtable.reloadData(this.tableData);
          }
        });
          this.loading = false
        } else {
            this.loading = false
          this.$message.error('获取列表失败')
        }
      },
      formatNumberWithThousandSeparator(value){
        if (value === null || value === undefined) return value;
        return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
      },
      //每页数量改变
      Sizechange(val) {
        this.ListInfo.currentPage = 1;
        this.ListInfo.pageSize = val;
        this.getList()
      },
      //当前页改变
      Pagechange(val) {
        this.ListInfo.currentPage = val;
        this.getList()
      },
      sortchange({ order, prop }) {
        if (prop) {
          this.ListInfo.orderBy = prop
          this.ListInfo.isAsc = order.indexOf("descending") == -1 ? true : false
          this.getList()
        }
      },
    }
  }
  </script>

  <style scoped lang="scss">
  .top {
    display: flex;
    margin-bottom: 10px;

    .publicCss {
      width: 150px;
      margin-right: 5px;
    }
  }

  ::v-deep .row-green {
    background-color: rgb(247, 230, 193);
    // color: #fff;
  }
  ::v-deep .row-bagreen {
    background-color: rgb(243, 138, 138);
    // color: #fff;
  }
  ::v-deep(.mytable-style.vxe-table .vxe-header--column.col-blue) {
    background-color: #2db7f5;
    color: #fff;
  }
  ::v-deep(.mytable-style.vxe-table .vxe-body--column.col-red) {
    background-color: red;
    color: #fff;
  }
  :deep(.vxe-header--column){
    background: #00937e;
    color: white;
    font-weight: 600;
}
:deep(.vxe-footer--row){
    background: #00937e;
    color: white;
    font-weight: 600;
}

.footer-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 8px 3px;
  background-color: #fafafa;
  border-top: 1px solid #e6e6e6;
}

.total-count-badge {
  display: inline-block;
  font-size: 13px;
  min-width: 35.5px;
  height: 28px;
  line-height: 28px;
  vertical-align: top;
  box-sizing: border-box;
  color: #606266;
  background-color: #f5f7fa;
  padding: 0 2px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.display_centered {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}
  </style>
